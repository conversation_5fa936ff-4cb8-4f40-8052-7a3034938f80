﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SMSystem.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("SMSystem.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Icon similar to (Icon).
        /// </summary>
        internal static System.Drawing.Icon _2022_09_08_123734 {
            get {
                object obj = ResourceManager.GetObject("_2022_09_08_123734", resourceCulture);
                return ((System.Drawing.Icon)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap _2022_09_08_1237341 {
            get {
                object obj = ResourceManager.GetObject("_2022_09_08_1237341", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap about_32px {
            get {
                object obj = ResourceManager.GetObject("about_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap add_32px {
            get {
                object obj = ResourceManager.GetObject("add_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تمت عملية الاضافة بنجاح.
        /// </summary>
        internal static string AddNotificationText {
            get {
                return ResourceManager.GetString("AddNotificationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap chart_32px {
            get {
                object obj = ResourceManager.GetObject("chart_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap combo_chart_32px {
            get {
                object obj = ResourceManager.GetObject("combo_chart_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap Delete_32px {
            get {
                object obj = ResourceManager.GetObject("Delete_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد من هذا الاجراء. لا يمكن استرجاع البيانات.
        /// </summary>
        internal static string DeleteActionCaption {
            get {
                return ResourceManager.GetString("DeleteActionCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجراء حذف.
        /// </summary>
        internal static string DeleteActionText {
            get {
                return ResourceManager.GetString("DeleteActionText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تمت عملية الحذف بنجاح.
        /// </summary>
        internal static string DeleteNotificationText {
            get {
                return ResourceManager.GetString("DeleteNotificationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجراء حذف.
        /// </summary>
        internal static string DeleteOutComeText {
            get {
                return ResourceManager.GetString("DeleteOutComeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ان عملية الحذف هذه لا تؤثر على المواد ( المواد  المتوفرة). اذا كنت بصدد ازالة المواد ايضا يجب عليك التعديل ثم الازالة.
        /// </summary>
        internal static string DeletOutComeCaption {
            get {
                return ResourceManager.GetString("DeletOutComeCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap department_store_32px {
            get {
                object obj = ResourceManager.GetObject("department_store_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بعض البيانات لا يجب ان تتكرر مثل المعرف. تأكد من تغييرة ثم اعد المحاولة.
        /// </summary>
        internal static string DuplicateDataCaption {
            get {
                return ResourceManager.GetString("DuplicateDataCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تكرار البيانات.
        /// </summary>
        internal static string DuplicateDataText {
            get {
                return ResourceManager.GetString("DuplicateDataText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap edit_32px {
            get {
                object obj = ResourceManager.GetObject("edit_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تمت عملية التعديل بنجاح.
        /// </summary>
        internal static string EditNotificationText {
            get {
                return ResourceManager.GetString("EditNotificationText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يوجد بيانات. استخدم الازار اعلاه لاجراء العمليات على البيانات.
        /// </summary>
        internal static string EmptyDataText {
            get {
                return ResourceManager.GetString("EmptyDataText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شبكة عرض البيانات فارغة. تأكد من اختيار صف لاتمام العملية.
        /// </summary>
        internal static string EmptyGridViewCaption {
            get {
                return ResourceManager.GetString("EmptyGridViewCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شبكة عرض البيانات لديك فارغة.
        /// </summary>
        internal static string EmptyGridViewText {
            get {
                return ResourceManager.GetString("EmptyGridViewText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الحقول التي تحتوي على علامة * هي مطلوبة. تأكد من ادخالها ثم اعد المحاولة.
        /// </summary>
        internal static string FiledsEmptyCaption {
            get {
                return ResourceManager.GetString("FiledsEmptyCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بعض الحقول مطلوبة.
        /// </summary>
        internal static string FiledsEmptyText {
            get {
                return ResourceManager.GetString("FiledsEmptyText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مخزن.
        /// </summary>
        internal static string GeneralText {
            get {
                return ResourceManager.GetString("GeneralText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap help_32px {
            get {
                object obj = ResourceManager.GetObject("help_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap home_32px {
            get {
                object obj = ResourceManager.GetObject("home_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_box_96px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_box_96px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_Close_80px {
            get {
                object obj = ResourceManager.GetObject("icons8_Close_80px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_csv_80px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_csv_80px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_customer_80px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_customer_80px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_damaged_parcel_32px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_damaged_parcel_32px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_damaged_parcel_96px {
            get {
                object obj = ResourceManager.GetObject("icons8_damaged_parcel_96px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_data_backup_80px {
            get {
                object obj = ResourceManager.GetObject("icons8_data_backup_80px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_database_restore_80px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_database_restore_80px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_export_32px {
            get {
                object obj = ResourceManager.GetObject("icons8_export_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_external_32px {
            get {
                object obj = ResourceManager.GetObject("icons8_external_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_external_80px {
            get {
                object obj = ResourceManager.GetObject("icons8_external_80px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_image_200px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_image_200px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_internal_32px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_internal_32px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_internal_96px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_internal_96px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_memo_32px {
            get {
                object obj = ResourceManager.GetObject("icons8_memo_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_moleskine_32px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_moleskine_32px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_near_me_160px {
            get {
                object obj = ResourceManager.GetObject("icons8_near_me_160px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_Notification_128px {
            get {
                object obj = ResourceManager.GetObject("icons8_Notification_128px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_ok_200px {
            get {
                object obj = ResourceManager.GetObject("icons8_ok_200px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_printer_96px {
            get {
                object obj = ResourceManager.GetObject("icons8_printer_96px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_scan_stock_128px {
            get {
                object obj = ResourceManager.GetObject("icons8_scan_stock_128px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_select_users_32px {
            get {
                object obj = ResourceManager.GetObject("icons8_select_users_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_select_users_32px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_select_users_32px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_supplier_160px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_supplier_160px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_users_96px_1 {
            get {
                object obj = ResourceManager.GetObject("icons8_users_96px_1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap icons8_Walmart_200px {
            get {
                object obj = ResourceManager.GetObject("icons8_Walmart_200px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى ادخال قيمة صحيحة.
        /// </summary>
        internal static string InvalidInputCaption {
            get {
                return ResourceManager.GetString("InvalidInputCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ ادخال.
        /// </summary>
        internal static string InvalidIputText {
            get {
                return ResourceManager.GetString("InvalidIputText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap Loading_icon {
            get {
                object obj = ResourceManager.GetObject("Loading_icon", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap Notification_128px {
            get {
                object obj = ResourceManager.GetObject("Notification_128px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سيتم نقل المادة الى قسم المواد التالفة.
        /// </summary>
        internal static string OnDamgeCaption {
            get {
                return ResourceManager.GetString("OnDamgeCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجراء اتلاف.
        /// </summary>
        internal static string OnDamgeText {
            get {
                return ResourceManager.GetString("OnDamgeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap open_box_32px {
            get {
                object obj = ResourceManager.GetObject("open_box_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سيتم نقل المادة الى قسم المواد التي خارج الذمة.
        /// </summary>
        internal static string OutOfConscinceCaption {
            get {
                return ResourceManager.GetString("OutOfConscinceCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نقل خارج الذمة.
        /// </summary>
        internal static string OutOfConscinceText {
            get {
                return ResourceManager.GetString("OutOfConscinceText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap print_32px {
            get {
                object obj = ResourceManager.GetObject("print_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يمكن ان تكون الكمية المضافة اكبر من الكمية المتوفرة, انظر الى الكمية للمادة الحالية وتأكد من انها اكبر او لا تساوي صفر ثم اعد المحاولة.
        /// </summary>
        internal static string QuantityMaterialCaption {
            get {
                return ResourceManager.GetString("QuantityMaterialCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كمية غير صحيحة.
        /// </summary>
        internal static string QuantityMaterialText {
            get {
                return ResourceManager.GetString("QuantityMaterialText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap refresh_32px {
            get {
                object obj = ResourceManager.GetObject("refresh_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap save_32px {
            get {
                object obj = ResourceManager.GetObject("save_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يبدو انك لم تقم بعملية الحفظ ولديك بيانات مضافة. سوف نحتفظ بها ويمكنك الرجوع لها في عملية الاضافة القادمة.
        /// </summary>
        internal static string SavedDataCaption {
            get {
                return ResourceManager.GetString("SavedDataCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تذكر !.
        /// </summary>
        internal static string SavedDataText {
            get {
                return ResourceManager.GetString("SavedDataText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap search_32px {
            get {
                object obj = ResourceManager.GetObject("search_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يبدو ان لديك مشكلة في في هذه العملية وهذا يعزى الى عده اسباب من بينها علاقة تربط هذه البيانات في بيانات اخرى يجب حذفها اولا او ريما عدم صحة تثبيت السيرفر او صلاحيات الوصول او عدم تثبيت قاعدة البيانات او خطأ في الاتصال في قاعدة البيانات. يمكنك تعديل نص الاتصال في اعدادات النظام او يمكنك الاتصال في مدير النظام لحل المشكلة.
        /// </summary>
        internal static string ServerConnectionCaption {
            get {
                return ResourceManager.GetString("ServerConnectionCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ في الاتصال في قاعدة البيانات.
        /// </summary>
        internal static string ServerConnectionText {
            get {
                return ResourceManager.GetString("ServerConnectionText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap settings_32px {
            get {
                object obj = ResourceManager.GetObject("settings_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ان هذا الاجراء يؤدي الى حذف المادة بشكل كامل من هنا وايضا من المواد.
        /// </summary>
        internal static string ShowDeleteMaterialCaption {
            get {
                return ResourceManager.GetString("ShowDeleteMaterialCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تؤدي هذه العملية الى حذف المادة بشكل كامل وسيتم التأثير في المواد .
        /// </summary>
        internal static string ShowMoveDamgeCaption {
            get {
                return ResourceManager.GetString("ShowMoveDamgeCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف مادة.
        /// </summary>
        internal static string ShowMoveDamgeText {
            get {
                return ResourceManager.GetString("ShowMoveDamgeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لحساسية اجراء الحذف لابد لك ان تقوم بأختيار كامل السطر, واذا اردت حذف كامل البيانات يمكنك الضغط CTRL+A.
        /// </summary>
        internal static string ShowRowsCaption {
            get {
                return ResourceManager.GetString("ShowRowsCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من اختيار كامل السطر لحذفة.
        /// </summary>
        internal static string ShowRowText {
            get {
                return ResourceManager.GetString("ShowRowText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap sorting_32px {
            get {
                object obj = ResourceManager.GetObject("sorting_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap supplier_32px {
            get {
                object obj = ResourceManager.GetObject("supplier_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap users_32px {
            get {
                object obj = ResourceManager.GetObject("users_32px", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
    }
}
