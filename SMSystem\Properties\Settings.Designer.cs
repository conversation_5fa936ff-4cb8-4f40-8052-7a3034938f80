﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SMSystem.Properties {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "16.10.0.0")]
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase {
        
        private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));
        
        public static Settings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("Server=.\\SQLEXPRESS;DataBase=DataBaseSMSystem;Integrated Security=True")]
        public string SQLServerConString {
            get {
                return ((string)(this["SQLServerConString"]));
            }
            set {
                this["SQLServerConString"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("3000")]
        public int NotificationTime {
            get {
                return ((int)(this["NotificationTime"]));
            }
            set {
                this["NotificationTime"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string User {
            get {
                return ((string)(this["User"]));
            }
            set {
                this["User"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string Role {
            get {
                return ((string)(this["Role"]));
            }
            set {
                this["Role"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("(د.ع)")]
        public string Currency {
            get {
                return ((string)(this["Currency"]));
            }
            set {
                this["Currency"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("اسم الشركة")]
        public string CompanyName {
            get {
                return ((string)(this["CompanyName"]));
            }
            set {
                this["CompanyName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("وصف الشركة")]
        public string CompanyDescritption {
            get {
                return ((string)(this["CompanyDescritption"]));
            }
            set {
                this["CompanyDescritption"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string CompanyLogo {
            get {
                return ((string)(this["CompanyLogo"]));
            }
            set {
                this["CompanyLogo"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string LastBackUpDate {
            get {
                return ((string)(this["LastBackUpDate"]));
            }
            set {
                this["LastBackUpDate"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("30")]
        public int NotificationDamagDuration {
            get {
                return ((int)(this["NotificationDamagDuration"]));
            }
            set {
                this["NotificationDamagDuration"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("اسم الجهة العليا")]
        public string HigherEmpName {
            get {
                return ((string)(this["HigherEmpName"]));
            }
            set {
                this["HigherEmpName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("اسم الجهة الدنيا")]
        public string LowerEmpName {
            get {
                return ((string)(this["LowerEmpName"]));
            }
            set {
                this["LowerEmpName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("الوظيفة")]
        public string HihgerEmpTitle {
            get {
                return ((string)(this["HihgerEmpTitle"]));
            }
            set {
                this["HihgerEmpTitle"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("الوظيفة")]
        public string LowerEmpTitle {
            get {
                return ((string)(this["LowerEmpTitle"]));
            }
            set {
                this["LowerEmpTitle"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2022 /     /")]
        public string HigherEmpDate {
            get {
                return ((string)(this["HigherEmpDate"]));
            }
            set {
                this["HigherEmpDate"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2022 /     /")]
        public string LowerEmpDate {
            get {
                return ((string)(this["LowerEmpDate"]));
            }
            set {
                this["LowerEmpDate"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute(" /     / 2022")]
        public string TextBoxReciveDate {
            get {
                return ((string)(this["TextBoxReciveDate"]));
            }
            set {
                this["TextBoxReciveDate"] = value;
            }
        }
    }
}
